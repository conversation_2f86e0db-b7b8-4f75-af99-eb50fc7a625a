const ldap = require('ldapjs');
const User = require('../models/User');
const AuditLog = require('../models/AuditLog');

class LDAPService {
  constructor() {
    this.ldapConfig = {
      url: process.env.LDAP_URL || 'ldap://your-domain-controller:389',
      bindDN: process.env.LDAP_BIND_DN || 'cn=admin,dc=yourcompany,dc=com',
      bindPassword: process.env.LDAP_BIND_PASSWORD || 'your-bind-password',
      searchBase: process.env.LDAP_SEARCH_BASE || 'ou=users,dc=yourcompany,dc=com',
      searchFilter: process.env.LDAP_SEARCH_FILTER || '(mail={{username}})',
      groupSearchBase: process.env.LDAP_GROUP_SEARCH_BASE || 'ou=groups,dc=yourcompany,dc=com'
    };

    // AD Group to Application Role Mapping
    this.roleMapping = {
      'CN=IT-Administrators,OU=Groups,DC=actia,DC=com': 'master_admin',
      'CN=Country-Managers,OU=Groups,DC=actia,DC=com': 'admin', 
      'CN=Department-Managers,OU=Groups,DC=actia,DC=com': 'manager',
      'CN=All-Employees,OU=Groups,DC=actia,DC=com': 'user'
    };

    // Department mapping
    this.departmentMapping = {
      'Information Technology': 'IT',
      'Engineering': 'Engineering',
      'Quality Assurance': 'Quality',
      'Production': 'Production',
      'Sales': 'Sales',
      'Human Resources': 'HR'
    };

    // Country mapping based on AD location
    this.countryMapping = {
      'France': 'France',
      'Germany': 'Germany', 
      'Spain': 'Spain',
      'Toulouse': 'France',
      'Munich': 'Germany',
      'Madrid': 'Spain',
      'Tunis': 'tunis'
    };
  }

  // Create LDAP client
  createClient() {
    const client = ldap.createClient({
      url: this.ldapConfig.url,
      timeout: 10000,
      connectTimeout: 10000,
      reconnect: true
    });

    return client;
  }

  // Authenticate user against LDAP/AD
  async authenticateUser(email, password) {
    return new Promise((resolve, reject) => {
      const client = this.createClient();
      
      // First, bind with service account to search for user
      client.bind(this.ldapConfig.bindDN, this.ldapConfig.bindPassword, (bindErr) => {
        if (bindErr) {
          console.error('LDAP bind error:', bindErr);
          client.unbind();
          return reject(new Error('LDAP connection failed'));
        }

        // Search for user
        const searchFilter = this.ldapConfig.searchFilter.replace('{{username}}', email);
        const searchOptions = {
          scope: 'sub',
          filter: searchFilter,
          attributes: [
            'mail', 'givenName', 'sn', 'displayName', 'department', 
            'title', 'memberOf', 'distinguishedName', 'l', 'co'
          ]
        };

        client.search(this.ldapConfig.searchBase, searchOptions, (searchErr, searchRes) => {
          if (searchErr) {
            console.error('LDAP search error:', searchErr);
            client.unbind();
            return reject(new Error('User search failed'));
          }

          let userFound = false;
          let userDN = null;
          let userData = null;

          searchRes.on('searchEntry', (entry) => {
            userFound = true;
            userDN = entry.objectName;
            userData = entry.object;
          });

          searchRes.on('error', (err) => {
            console.error('LDAP search result error:', err);
            client.unbind();
            reject(new Error('User search failed'));
          });

          searchRes.on('end', () => {
            if (!userFound) {
              client.unbind();
              return reject(new Error('User not found in directory'));
            }

            // Now try to bind with user credentials to verify password
            const userClient = this.createClient();
            userClient.bind(userDN, password, (userBindErr) => {
              userClient.unbind();
              client.unbind();

              if (userBindErr) {
                console.error('User authentication failed:', userBindErr);
                return reject(new Error('Invalid credentials'));
              }

              // Authentication successful, return user data
              resolve(this.parseUserData(userData));
            });
          });
        });
      });
    });
  }

  // Parse user data from LDAP response
  parseUserData(ldapUser) {
    const groups = Array.isArray(ldapUser.memberOf) ? ldapUser.memberOf : [ldapUser.memberOf].filter(Boolean);
    
    return {
      email: ldapUser.mail,
      firstName: ldapUser.givenName,
      lastName: ldapUser.sn,
      displayName: ldapUser.displayName,
      department: this.mapDepartment(ldapUser.department),
      title: ldapUser.title,
      role: this.mapRole(groups),
      country: this.mapCountry(ldapUser.l || ldapUser.co),
      ldapDN: ldapUser.distinguishedName,
      groups: groups
    };
  }

  // Map LDAP groups to application roles
  mapRole(groups) {
    // Check groups in priority order (highest privilege first)
    const roleOrder = ['master_admin', 'admin', 'manager', 'user'];
    
    for (const role of roleOrder) {
      for (const group of groups) {
        if (Object.entries(this.roleMapping).find(([dn, mappedRole]) => 
          mappedRole === role && group.includes(dn.split(',')[0].replace('CN=', ''))
        )) {
          return role;
        }
      }
    }
    
    // Default role if no groups match
    return 'user';
  }

  // Map LDAP department to application department
  mapDepartment(ldapDepartment) {
    if (!ldapDepartment) return 'Engineering'; // Default
    
    return this.departmentMapping[ldapDepartment] || ldapDepartment;
  }

  // Map LDAP location to application country
  mapCountry(ldapLocation) {
    if (!ldapLocation) return 'Tunis'; // Default
    
    return this.countryMapping[ldapLocation] || 'Tunis';
  }

  // Sync user from LDAP to database
  async syncUserFromLDAP(email) {
    try {
      const client = this.createClient();
      
      return new Promise((resolve, reject) => {
        client.bind(this.ldapConfig.bindDN, this.ldapConfig.bindPassword, (bindErr) => {
          if (bindErr) {
            client.unbind();
            return reject(new Error('LDAP connection failed'));
          }

          const searchFilter = this.ldapConfig.searchFilter.replace('{{username}}', email);
          const searchOptions = {
            scope: 'sub',
            filter: searchFilter,
            attributes: [
              'mail', 'givenName', 'sn', 'displayName', 'department',
              'title', 'memberOf', 'distinguishedName', 'l', 'co'
            ]
          };

          client.search(this.ldapConfig.searchBase, searchOptions, (searchErr, searchRes) => {
            if (searchErr) {
              client.unbind();
              return reject(new Error('User search failed'));
            }

            let userFound = false;
            let userData = null;

            searchRes.on('searchEntry', (entry) => {
              userFound = true;
              userData = entry.object;
            });

            searchRes.on('end', async () => {
              client.unbind();
              
              if (!userFound) {
                return reject(new Error('User not found in directory'));
              }

              try {
                const parsedUser = this.parseUserData(userData);
                
                // Update or create user in database
                const user = await User.findOneAndUpdate(
                  { email: parsedUser.email },
                  {
                    ...parsedUser,
                    lastLDAPSync: new Date(),
                    isActive: true
                  },
                  { 
                    upsert: true, 
                    new: true,
                    setDefaultsOnInsert: true
                  }
                );

                resolve(user);
              } catch (dbError) {
                reject(new Error(`Database update failed: ${dbError.message}`));
              }
            });
          });
        });
      });
    } catch (error) {
      console.error('LDAP sync error:', error);
      throw error;
    }
  }

  // Test LDAP connection
  async testConnection() {
    return new Promise((resolve, reject) => {
      const client = this.createClient();
      
      client.bind(this.ldapConfig.bindDN, this.ldapConfig.bindPassword, (err) => {
        client.unbind();
        
        if (err) {
          console.error('LDAP connection test failed:', err);
          reject(new Error('LDAP connection failed'));
        } else {
          console.log('LDAP connection successful');
          resolve(true);
        }
      });
    });
  }

  // Get LDAP configuration (without sensitive data)
  getConfig() {
    return {
      url: this.ldapConfig.url,
      searchBase: this.ldapConfig.searchBase,
      searchFilter: this.ldapConfig.searchFilter,
      roleMapping: this.roleMapping,
      departmentMapping: this.departmentMapping,
      countryMapping: this.countryMapping
    };
  }
}

module.exports = new LDAPService();
